directories:
  output: final
  buildResources: build
appId: com.passwordmanager.app
productName: Password Manager
files:
  - filter:
      - '**/*'
      - '!node_modules/**/*'
      - '!dist/**/*'
      - '!build/**/*'
      - '!*.md'
      - '!.git/**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: assets/icon.png
  requestedExecutionLevel: asInvoker
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Password Manager
portable:
  artifactName: PasswordManager-Portable-${version}.exe
electronVersion: 27.3.11
