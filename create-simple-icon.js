const fs = require('fs');
const path = require('path');

// Создаем простую PNG иконку в base64 формате
function createSimpleIcon() {
    // Это минимальная PNG иконка 16x16 пикселей (синий квадрат)
    const pngData = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, // IHDR chunk size
        0x49, 0x48, 0x44, 0x52, // IHDR
        0x00, 0x00, 0x00, 0x10, // width: 16
        0x00, 0x00, 0x00, 0x10, // height: 16
        0x08, 0x02, 0x00, 0x00, 0x00, // bit depth: 8, color type: 2 (RGB), compression: 0, filter: 0, interlace: 0
        0x90, 0x91, 0x68, 0x36, // CRC
        
        0x00, 0x00, 0x00, 0x0C, // IDAT chunk size
        0x49, 0x44, 0x41, 0x54, // IDAT
        0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, // compressed data
        0x00, 0x01, 0x00, 0x25, // CRC
        
        0x00, 0x00, 0x00, 0x00, // IEND chunk size
        0x49, 0x45, 0x4E, 0x44, // IEND
        0xAE, 0x42, 0x60, 0x82  // CRC
    ]);
    
    const iconPath = path.join(__dirname, 'assets', 'icon.png');
    fs.writeFileSync(iconPath, pngData);
    console.log('Simple PNG icon created:', iconPath);
}

// Создаем более качественную иконку с помощью простого рисования
function createBetterIcon() {
    // Создаем простую 256x256 PNG иконку
    const width = 256;
    const height = 256;
    
    // Создаем простой PNG заголовок
    const pngSignature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
    
    // IHDR chunk
    const ihdrData = Buffer.alloc(13);
    ihdrData.writeUInt32BE(width, 0);
    ihdrData.writeUInt32BE(height, 4);
    ihdrData[8] = 8; // bit depth
    ihdrData[9] = 2; // color type (RGB)
    ihdrData[10] = 0; // compression
    ihdrData[11] = 0; // filter
    ihdrData[12] = 0; // interlace
    
    const ihdrChunk = Buffer.concat([
        Buffer.from([0x00, 0x00, 0x00, 0x0D]), // chunk size
        Buffer.from('IHDR'),
        ihdrData,
        Buffer.from([0x90, 0x91, 0x68, 0x36]) // CRC (placeholder)
    ]);
    
    // Простые данные изображения (синий фон)
    const imageData = Buffer.alloc(width * height * 3); // RGB
    for (let i = 0; i < imageData.length; i += 3) {
        imageData[i] = 0x3B;     // R - синий
        imageData[i + 1] = 0x82; // G
        imageData[i + 2] = 0xF6; // B
    }
    
    // IDAT chunk (упрощенный)
    const idatChunk = Buffer.concat([
        Buffer.from([0x00, 0x00, 0x00, 0x0C]), // chunk size (placeholder)
        Buffer.from('IDAT'),
        Buffer.from([0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02]), // compressed data (placeholder)
        Buffer.from([0x00, 0x01, 0x00, 0x25]) // CRC (placeholder)
    ]);
    
    // IEND chunk
    const iendChunk = Buffer.from([
        0x00, 0x00, 0x00, 0x00, // chunk size
        0x49, 0x45, 0x4E, 0x44, // IEND
        0xAE, 0x42, 0x60, 0x82  // CRC
    ]);
    
    const pngBuffer = Buffer.concat([pngSignature, ihdrChunk, idatChunk, iendChunk]);
    
    const iconPath = path.join(__dirname, 'assets', 'icon.png');
    fs.writeFileSync(iconPath, pngBuffer);
    console.log('Better PNG icon created:', iconPath);
}

// Создаем иконку из SVG данных (упрощенная версия)
function createIconFromSVGData() {
    // Читаем SVG файл
    const svgPath = path.join(__dirname, 'assets', 'icon.svg');
    
    if (fs.existsSync(svgPath)) {
        console.log('SVG file exists, creating PNG version...');
        
        // Создаем простую PNG иконку как заглушку
        createSimpleIcon();
        
        console.log('PNG icon created from SVG template');
        console.log('For better quality, manually convert SVG to PNG using:');
        console.log('- Online converter: https://convertio.co/svg-png/');
        console.log('- Or use image editing software');
        
        return true;
    }
    
    return false;
}

if (require.main === module) {
    console.log('Creating application icon...');
    
    if (!createIconFromSVGData()) {
        createSimpleIcon();
    }
    
    console.log('Icon creation completed!');
}

module.exports = { createSimpleIcon, createBetterIcon, createIconFromSVGData };
