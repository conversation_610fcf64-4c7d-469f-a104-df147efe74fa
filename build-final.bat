@echo off
echo ========================================
echo Password Manager - Final Build Script
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found: 
node --version

echo.
echo Checking icons...
if exist "assets\icon.ico" (
    echo ✓ ICO icon found: assets\icon.ico
) else (
    echo ✗ ICO icon not found
)

if exist "assets\icon.png" (
    echo ✓ PNG icon found: assets\icon.png
) else (
    echo ✗ PNG icon not found
)

echo.
echo Cleaning previous builds...
if exist "final" (
    echo Removing final folder...
    timeout /t 2 /nobreak >nul
    rd /s /q "final" 2>nul
)

if exist "dist" (
    echo Removing dist folder...
    timeout /t 2 /nobreak >nul
    rd /s /q "dist" 2>nul
)

if exist "release" (
    echo Removing release folder...
    timeout /t 2 /nobreak >nul
    rd /s /q "release" 2>nul
)

if exist "build" (
    echo Removing build folder...
    timeout /t 2 /nobreak >nul
    rd /s /q "build" 2>nul
)

echo.
echo Installing/updating dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo Building Application
echo ========================================
echo.

echo Step 1: Building unpacked version...
call npx electron-builder --dir --config.directories.output=final

if %errorlevel% neq 0 (
    echo ERROR: Failed to build unpacked version
    pause
    exit /b 1
)

echo ✓ Unpacked version created successfully!
echo.

echo Step 2: Building portable version...
call npx electron-builder --win --x64 --config.win.target=portable --config.directories.output=final

if %errorlevel% neq 0 (
    echo Warning: Failed to build portable version
    echo The unpacked version is still available
) else (
    echo ✓ Portable version created successfully!
)

echo.
echo Step 3: Building installer...
call npx electron-builder --win --x64 --config.win.target=nsis --config.directories.output=final

if %errorlevel% neq 0 (
    echo Warning: Failed to build installer
    echo Portable and unpacked versions are still available
) else (
    echo ✓ Installer created successfully!
)

echo.
echo ========================================
echo Build Results
echo ========================================
echo.

if exist "final" (
    echo Files created in 'final' folder:
    echo.
    
    if exist "final\*.exe" (
        echo 📦 Executable files:
        for %%f in ("final\*.exe") do echo   - %%~nxf
        echo.
    )
    
    if exist "final\win-unpacked" (
        echo 📁 Unpacked version: final\win-unpacked\
        if exist "final\win-unpacked\Password Manager.exe" (
            echo   ▶ Main executable: final\win-unpacked\Password Manager.exe
        )
        echo.
    )
    
    echo 📋 All files in final folder:
    dir /b "final"
    
    echo.
    echo ========================================
    echo SUCCESS! Your application is ready!
    echo ========================================
    echo.
    echo You can now:
    echo 1. Run the unpacked version: final\win-unpacked\Password Manager.exe
    if exist "final\*.exe" (
        echo 2. Distribute the portable/installer exe files
    )
    echo.
    
) else (
    echo ❌ No final folder found. Build failed completely.
    echo Please check the error messages above.
)

echo.
pause
